'use client';

import { useState, useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { type ForwardedRef } from 'react';
// Import our custom styles only for now
import '../styles/ckeditor.css';

// Type definitions
interface MyCKEditorProps {
  initialData?: string;
  onChange?: (data: string) => void;
  placeholder?: string;
  height?: number;
  className?: string;
  disabled?: boolean;
  config?: any;
  onReady?: (editor: any) => void;
  onError?: (error: Error) => void;
}

interface MyCKEditorRef {
  getEditor: () => any;
  getContent: () => string;
  setContent: (content: string) => void;
  focus: () => void;
  toggleSourceMode: () => void;
}

const MyCKEditor = forwardRef<MyCKEditorRef, MyCKEditorProps>(({
  initialData = '',
  onChange,
  placeholder = 'Ketik konten di sini...',
  height = 600,
  className = '',
  disabled = false,
  config = {},
  onReady,
  onError
}, ref: ForwardedRef<MyCKEditorRef>) => {
  // State management
  const [content, setContent] = useState(initialData);
  const [editorState, setEditorState] = useState<'loading' | 'ready' | 'error'>('loading');
  const [error, setError] = useState<string | null>(null);
  const [isSourceMode, setIsSourceMode] = useState(false);

  // Refs for dynamic imports
  const CKEditor = useRef<any>(null);
  const CustomClassicEditor = useRef<any>(null);
  const editorRef = useRef<any>(null);

  // Expose editor instance through ref
  useImperativeHandle(ref, () => ({
    getEditor: () => editorRef.current,
    getContent: () => content,
    setContent: (newContent: string) => {
      setContent(newContent);
      if (editorRef.current) {
        editorRef.current.setData(newContent);
      }
    },
    focus: () => {
      if (editorRef.current) {
        editorRef.current.editing.view.focus();
      }
    },
    toggleSourceMode: () => {
      if (editorRef.current) {
        editorRef.current.execute('sourceEditing');
        setIsSourceMode(!isSourceMode);
      }
    }
  }));

  // Load CKEditor dynamically
  useEffect(() => {
    let isMounted = true;

    const loadEditor = async (): Promise<void> => {
      try {
        setEditorState('loading');
        
        if (typeof window === 'undefined') {
          return;
        }

        // Try to load custom build first, fallback to standard build
        try {
          const [ckeditorReact, customEditorFactory] = await Promise.all([
            import('@ckeditor/ckeditor5-react'),
            import('../../ckeditor5/build/ckeditor.js')
          ]);
          
          // Get the actual editor class from the factory function
          const customEditor = await customEditorFactory.default();
          
          if (isMounted) {
            CKEditor.current = ckeditorReact.CKEditor;
            CustomClassicEditor.current = customEditor;
            setEditorState('ready');
            setError(null);
          }
        } catch (customError: any) {
          console.warn('Custom CKEditor build not available, falling back to simpler editor:', customError.message);
          
          // Fallback to a simple editor configuration
          const ckeditorReact = await import('@ckeditor/ckeditor5-react');
          
          // Import the main CKEditor 5 module with essential plugins
          const ckeditor5 = await import('ckeditor5');
          
          // Create a minimal editor class
          class StandardEditor extends ckeditor5.ClassicEditor {
            static builtinPlugins = [
              ckeditor5.Essentials,
              ckeditor5.Paragraph,
              ckeditor5.Bold,
              ckeditor5.Italic,
              ckeditor5.Link,
              ckeditor5.List,
              ckeditor5.Heading,
              ckeditor5.BlockQuote
            ];
            
            static defaultConfig = {
              toolbar: [
                'heading',
                '|',
                'bold',
                'italic',
                'link',
                '|',
                'bulletedList',
                'numberedList',
                'blockQuote'
              ],
              placeholder: 'Ketik konten di sini...'
            };
          }
          
          if (isMounted) {
            CKEditor.current = ckeditorReact.CKEditor;
            CustomClassicEditor.current = StandardEditor;
            setEditorState('ready');
            setError(null);
          }
        }
      } catch (err: any) {
        console.error('Failed to load CKEditor:', err);
        if (isMounted) {
          setError(err.message);
          setEditorState('error');
        }
      }
    };

    loadEditor();

    return () => {
      isMounted = false;
    };
  }, []);

  // Update content when initialData changes
  useEffect(() => {
    if (initialData !== content) {
      setContent(initialData);
    }
  }, [initialData, content]);

  // Handle editor content changes
  const handleEditorChange = (event: any, editor: any) => {
    const newData = editor.getData();
    setContent(newData);
    if (onChange) {
      onChange(newData);
    }
  };

  // Enhanced editor configuration
  const editorConfig = {
    placeholder: placeholder,
    language: 'id',
    licenseKey: 'GPL',
    ...config
  };

  // Loading state
  if (editorState === 'loading') {
    return (
      <div 
        className={`flex items-center justify-center p-4 border border-gray-300 rounded-md bg-gray-50 ${className}`}
        style={{ minHeight: height }}
      >
        <div className="w-8 h-8 border-4 rounded-full border-t-blue-600 animate-spin"></div>
        <span className="ml-3 text-sm text-gray-500">Loading CKEditor...</span>
      </div>
    );
  }

  // Error state
  if (editorState === 'error') {
    return (
      <div className={`p-4 text-red-700 bg-red-100 border border-red-300 rounded-md ${className}`}>
        <p className="font-medium">Error loading CKEditor:</p>
        <p className="text-sm">{error}</p>
        <textarea
          value={content}
          onChange={(e) => {
            setContent(e.target.value);
            if (onChange) onChange(e.target.value);
          }}
          placeholder={placeholder}
          className="w-full p-3 mt-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          style={{ minHeight: height - 100 }}
          disabled={disabled}
        />
      </div>
    );
  }

  // Success state with CKEditor
  if (editorState === 'ready') {
    return (
      <div className={`ck-editor-wrapper ${className}`}>
        <style jsx global>{`
          .ck-editor-wrapper .ck-editor__editable {
            min-height: ${height - 60}px !important;
          }
        `}</style>

        <CKEditor.current
          editor={CustomClassicEditor.current}
          data={content}
          config={editorConfig}
          disabled={disabled}
          onChange={handleEditorChange}
          onReady={(editor: any) => {
            editorRef.current = editor;
            if (onReady) {
              onReady(editor);
            }
          }}
          onError={(error: Error, { willEditorRestart }: { willEditorRestart: boolean }) => {
            console.error('CKEditor error:', error);
            if (!willEditorRestart) {
              setError(error.message);
              setEditorState('error');
            }
            if (onError) {
              onError(error);
            }
          }}
        />
      </div>
    );
  }

  return null;
});

MyCKEditor.displayName = 'MyCKEditor';

export default MyCKEditor;
export type { MyCKEditorProps, MyCKEditorRef };
