/* ==========================================
   MODERN CKEDITOR 5 STYLING
   Professional & Clean Layout v4.0
   INCLUDES: Essential CKEditor CSS + Custom Enhancements
   ========================================== */

@import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400;500;600;700&display=swap');

/* ==========================================
   ESSENTIAL CKEDITOR 5 CSS VARIABLES
   Based on ckeditor5.css defaults
   ========================================== */

:root {
  /* CKEditor default variables (essential) */
  --ck-color-base-foreground: hsl(0, 0%, 98%);
  --ck-color-base-background: hsl(0, 0%, 100%);
  --ck-color-base-border: hsl(220, 6%, 81%);
  --ck-color-base-action: hsl(104, 50.2%, 42.5%);
  --ck-color-base-focus: hsl(209, 92%, 70%);
  --ck-color-base-text: hsl(0, 0%, 20%);
  --ck-color-base-active: hsl(218.1, 100%, 58%);
  --ck-color-base-active-focus: hsl(218.2, 100%, 52.5%);
  --ck-color-base-error: hsl(15, 100%, 43%);

  --ck-color-focus-border-coordinates: 218, 81.8%, 56.9%;
  --ck-color-focus-border: hsl(var(--ck-color-focus-border-coordinates));
  --ck-color-focus-outer-shadow: hsl(212.4, 89.3%, 89%);
  --ck-color-text: var(--ck-color-base-text);
  --ck-color-shadow-drop: hsla(0, 0%, 0%, 0.15);
  --ck-color-shadow-drop-active: hsla(0, 0%, 0%, 0.2);
  --ck-color-shadow-inner: hsla(0, 0%, 0%, 0.1);

  --ck-color-button-default-background: transparent;
  --ck-color-button-default-hover-background: hsl(0, 0%, 94.1%);
  --ck-color-button-default-active-background: hsl(0, 0%, 94.1%);
  --ck-color-button-on-background: hsl(212, 100%, 97.1%);
  --ck-color-button-on-hover-background: hsl(211.7, 100%, 92.9%);

  /* Custom variables for enhancement */
  --ck-font-family: 'Lato', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --ck-font-size-base: 14px;
  --ck-line-height-base: 1.6;
  --ck-border-radius: 8px;
  --ck-spacing-standard: 12px;
}

/* ==========================================
   ESSENTIAL CKEDITOR BASE STYLES
   Minimal required styles for proper rendering
   ========================================== */

.ck.ck-reset,
.ck.ck-reset_all,
.ck.ck-reset_all * {
  box-sizing: border-box;
  width: auto;
  height: auto;
  position: static;
}

.ck.ck-editor {
  position: relative;
}

.ck.ck-editor__main {
  display: block;
}

.ck.ck-editor__editable {
  position: relative;
  overflow-wrap: break-word;
  outline: none;
  border: none;
  padding: 0;
  margin: 0;
}

.ck.ck-editor__editable.ck-focused {
  outline: none;
}

.ck.ck-toolbar {
  display: flex;
  flex-direction: row;
  user-select: none;
  border: none;
  border-bottom: 1px solid var(--ck-color-base-border);
  background: var(--ck-color-base-foreground);
}

.ck.ck-toolbar .ck-toolbar__items {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  gap: 0;
}

.ck.ck-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  user-select: none;
  border: none;
  background: var(--ck-color-button-default-background);
  cursor: pointer;
  min-width: auto;
  min-height: auto;
  text-decoration: none;
  outline: none;
  transition: all 150ms ease;
}

.ck.ck-button:hover {
  background: var(--ck-color-button-default-hover-background);
}

.ck.ck-button.ck-on {
  background: var(--ck-color-button-on-background);
}

.ck.ck-button.ck-on:hover {
  background: var(--ck-color-button-on-hover-background);
}

.ck.ck-button__label {
  display: none;
}

.ck.ck-button .ck-button__icon {
  pointer-events: none;
  color: inherit;
}

.ck.ck-dropdown {
  display: inline-block;
  position: relative;
}

.ck.ck-dropdown__panel {
  display: none;
  position: absolute;
  z-index: 999;
  background: var(--ck-color-base-background);
  border: 1px solid var(--ck-color-base-border);
  border-radius: 2px;
  box-shadow: var(--ck-color-shadow-drop) 0 2px 8px;
}

.ck.ck-dropdown__panel.ck-dropdown__panel_visible {
  display: block;
}

/* ==========================================
   MAIN EDITOR CONTAINER - SAFE OVERRIDES
   ========================================== */

.ck-editor-wrapper {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
  border-radius: var(--ck-border-radius);
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background: var(--ck-color-base-background);
  font-family: var(--ck-font-family);
  /* Ensure our wrapper doesn't interfere with CKEditor internals */
  position: relative;
  z-index: 1;
}

/* ==========================================
   CRITICAL CSS LOADING FIX
   ========================================== */

/* Ensure CKEditor default styles are loaded properly */
.ck-editor-wrapper .ck-editor {
  /* Only enhance, don't override basic structure */
  border-radius: var(--ck-border-radius);
  font-family: var(--ck-font-family);
}

/* Hide any potential "CKEditor" text rendering */
.ck-editor-wrapper .ck-editor::before,
.ck-editor-wrapper::before {
  display: none !important;
  content: none !important;
}

/* Ensure editor content doesn't show as plain text */
.ck-editor-wrapper .ck-content {
  font-family: var(--ck-font-family) !important;
}

/* If CSS isn't loaded, provide minimal fallback */
.ck-editor-wrapper .ck-editor:not(.ck-focused):not(.ck-blurred) {
  border: 1px solid var(--ck-color-base-border);
  border-radius: var(--ck-border-radius);
}

/* ==========================================
   EDITOR CORE ELEMENTS - GENTLE ENHANCEMENT
   ========================================== */

.ck.ck-editor {
  border: 1px solid var(--ck-color-base-border) !important;
  border-radius: var(--ck-border-radius) !important;
  overflow: hidden !important;
  width: 100% !important;
  max-width: 100% !important;
  font-family: var(--ck-font-family) !important;
}

.ck.ck-editor__main {
  background: var(--ck-color-base-background) !important;
}

.ck.ck-editor__editable {
  border: none !important;
  border-radius: 0 !important;
  min-height: 400px !important;
  max-height: none !important;
  padding: 20px !important;
  font-family: var(--ck-font-family) !important;
  font-size: var(--ck-font-size-base) !important;
  line-height: var(--ck-line-height-base) !important;
  background: var(--ck-color-base-background) !important;
  color: #1f2937 !important;
}

.ck.ck-editor__editable:focus {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

/* ==========================================
   TOOLBAR STYLING - FIXED HORIZONTAL LAYOUT
   ========================================== */

.ck.ck-toolbar {
  border: none !important;
  border-bottom: 1px solid var(--ck-color-base-border) !important;
  background: #f9fafb !important;
  padding: 10px 15px !important;
  font-family: var(--ck-font-family) !important;
  
  /* CRITICAL: Ensure horizontal layout */
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: wrap !important;
  align-items: center !important;
  gap: 4px !important;
  
  /* Prevent vertical stacking */
  white-space: nowrap !important;
  overflow-x: auto !important;
  overflow-y: hidden !important;
  
  /* Scrollbar styling for horizontal overflow */
  scrollbar-width: thin !important;
  scrollbar-color: #cbd5e1 transparent !important;
}

.ck.ck-toolbar::-webkit-scrollbar {
  height: 6px !important;
}

.ck.ck-toolbar::-webkit-scrollbar-track {
  background: transparent !important;
}

.ck.ck-toolbar::-webkit-scrollbar-thumb {
  background: #cbd5e1 !important;
  border-radius: 3px !important;
}

.ck.ck-toolbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8 !important;
}

/* Toolbar groups and items */
.ck.ck-toolbar__items {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: wrap !important;
  align-items: center !important;
  gap: 4px !important;
}

.ck.ck-toolbar__separator {
  background: var(--ck-color-base-border) !important;
  width: 1px !important;
  height: 20px !important;
  margin: 0 8px !important;
  flex-shrink: 0 !important;
}

/* ==========================================
   TOOLBAR BUTTONS - IMPROVED LAYOUT
   ========================================== */

.ck.ck-button {
  border-radius: 4px !important;
  border: none !important;
  padding: 6px 8px !important;
  margin: 1px !important;
  background: transparent !important;
  
  /* Ensure consistent sizing */
  min-width: 32px !important;
  min-height: 32px !important;
  height: 32px !important;
  max-height: 32px !important;
  
  font-family: var(--ck-font-family) !important;
  font-size: 13px !important;
  transition: all 0.15s ease !important;
  
  /* Prevent text wrapping */
  white-space: nowrap !important;
  overflow: hidden !important;
  
  /* Flex properties for consistent alignment */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-shrink: 0 !important;
}

.ck.ck-button:hover {
  background: var(--ck-color-base-active) !important;
  border: none !important;
}

.ck.ck-button.ck-on {
  background: var(--ck-color-base-active) !important;
  color: var(--ck-color-focus-border) !important;
  border: none !important;
}

.ck.ck-button:focus {
  box-shadow: 0 0 0 2px var(--ck-color-focus-border) !important;
  outline: none !important;
  border: none !important;
}

.ck.ck-button .ck-button__label {
  font-family: var(--ck-font-family) !important;
  font-size: 13px !important;
  font-weight: 400 !important;
  white-space: nowrap !important;
}

.ck.ck-button .ck-button__icon {
  width: 16px !important;
  height: 16px !important;
  flex-shrink: 0 !important;
}

/* Special handling for dropdown buttons */
.ck.ck-splitbutton {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
}

.ck.ck-dropdown {
  border-radius: 4px !important;
  display: inline-flex !important;
  align-items: center !important;
}

.ck.ck-dropdown__button {
  border-radius: 4px !important;
  height: 32px !important;
  min-height: 32px !important;
  padding: 6px 8px !important;
}

/* ==========================================
   DROPDOWN MENUS - ENHANCED STYLING
   ========================================== */

.ck.ck-dropdown__panel {
  border: 1px solid var(--ck-color-base-border) !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  background: var(--ck-color-base-background) !important;
  margin-top: 4px !important;
  z-index: 9999 !important;
  max-height: 300px !important;
  overflow-y: auto !important;
}

.ck.ck-list {
  padding: 6px !important;
  margin: 0 !important;
  max-height: 250px !important;
  overflow-y: auto !important;
}

.ck.ck-list__item {
  border-radius: 4px !important;
  margin: 1px 0 !important;
}

.ck.ck-list__button {
  border-radius: 4px !important;
  padding: 8px 12px !important;
  font-family: var(--ck-font-family) !important;
  font-size: 14px !important;
  width: 100% !important;
  text-align: left !important;
  border: none !important;
  background: transparent !important;
  transition: background-color 0.15s ease !important;
}

.ck.ck-list__button:hover {
  background: var(--ck-color-base-active) !important;
}

.ck.ck-list__button:focus {
  background: var(--ck-color-base-active) !important;
  box-shadow: inset 0 0 0 2px var(--ck-color-focus-border) !important;
}

/* Font Size Dropdown Specific Styling */
.ck.ck-fontsize-dropdown .ck-list__button {
  font-family: var(--ck-font-family) !important;
  display: flex !important;
  align-items: center !important;
}

/* Font Color Picker Styling */
.ck.ck-color-picker {
  padding: 8px !important;
}

.ck.ck-color-picker__row {
  display: flex !important;
  gap: 2px !important;
  margin-bottom: 2px !important;
}

.ck.ck-color-picker__tile {
  width: 24px !important;
  height: 24px !important;
  border-radius: 3px !important;
  border: 1px solid #ccc !important;
  cursor: pointer !important;
}

.ck.ck-color-picker__tile:hover {
  border-color: var(--ck-color-focus-border) !important;
  transform: scale(1.1) !important;
}

/* ==========================================
   HEADING STYLES IN DROPDOWN
   ========================================== */

.ck.ck-heading-dropdown .ck-list__button {
  font-family: var(--ck-font-family) !important;
}

.ck.ck-heading-dropdown .ck-list__button[data-cke-tooltip-text*="Heading 1"] {
  font-size: 18px !important;
  font-weight: 600 !important;
}

.ck.ck-heading-dropdown .ck-list__button[data-cke-tooltip-text*="Heading 2"] {
  font-size: 16px !important;
  font-weight: 600 !important;
}

.ck.ck-heading-dropdown .ck-list__button[data-cke-tooltip-text*="Heading 3"] {
  font-size: 15px !important;
  font-weight: 500 !important;
}

/* ==========================================
   CONTENT TYPOGRAPHY
   ========================================== */

.ck-content {
  font-family: var(--ck-font-family) !important;
  font-size: var(--ck-font-size-base) !important;
  line-height: var(--ck-line-height-base) !important;
  color: #1f2937 !important;
}

.ck-content h1,
.ck-content h2,
.ck-content h3,
.ck-content h4,
.ck-content h5,
.ck-content h6 {
  font-family: var(--ck-font-family) !important;
  font-weight: 600 !important;
  margin: 1.5em 0 0.75em 0 !important;
  line-height: 1.4 !important;
}

.ck-content h1 { font-size: 2em !important; }
.ck-content h2 { font-size: 1.5em !important; }
.ck-content h3 { font-size: 1.25em !important; }
.ck-content h4 { font-size: 1.1em !important; }
.ck-content h5 { font-size: 1em !important; }
.ck-content h6 { font-size: 0.9em !important; }

.ck-content p {
  margin: 0 0 1em 0 !important;
  font-family: var(--ck-font-family) !important;
}

.ck-content ul,
.ck-content ol {
  margin: 1em 0 !important;
  padding-left: 2em !important;
}

.ck-content li {
  margin: 0.25em 0 !important;
}

.ck-content blockquote {
  border-left: 3px solid var(--ck-color-focus-border) !important;
  padding-left: 1.5em !important;
  margin: 1.5em 0 !important;
  font-style: italic !important;
  color: #6b7280 !important;
}

.ck-content a {
  color: var(--ck-color-focus-border) !important;
  text-decoration: underline !important;
}

.ck-content a:hover {
  color: #1d4ed8 !important;
}

/* ==========================================
   RESPONSIVE DESIGN - ENHANCED
   ========================================== */

@media (max-width: 768px) {
  .ck.ck-toolbar {
    padding: 8px 10px !important;
    gap: 2px !important;
    overflow-x: auto !important;
    overflow-y: hidden !important;
    
    /* Enable horizontal scrolling on mobile */
    -webkit-overflow-scrolling: touch !important;
    scrollbar-width: thin !important;
  }
  
  .ck.ck-button {
    padding: 4px 6px !important;
    height: 28px !important;
    min-height: 28px !important;
    min-width: 28px !important;
    font-size: 12px !important;
    flex-shrink: 0 !important;
  }
  
  .ck.ck-dropdown__button {
    height: 28px !important;
    min-height: 28px !important;
    padding: 4px 6px !important;
  }
  
  .ck.ck-editor__editable {
    padding: 15px !important;
    min-height: 300px !important;
    font-size: 13px !important;
  }
  
  /* Hide less important toolbar items on very small screens */
  .ck.ck-toolbar .ck-button[title*="Font Background Color"],
  .ck.ck-toolbar .ck-button[title*="Remove Format"] {
    display: none !important;
  }
}

@media (max-width: 480px) {
  .ck.ck-toolbar {
    padding: 6px 8px !important;
    flex-wrap: nowrap !important; /* Force horizontal scrolling */
  }
  
  .ck.ck-editor__editable {
    padding: 12px !important;
    min-height: 250px !important;
  }
  
  .ck.ck-button {
    height: 26px !important;
    min-height: 26px !important;
    min-width: 26px !important;
    padding: 3px 5px !important;
  }
  
  .ck.ck-dropdown__button {
    height: 26px !important;
    min-height: 26px !important;
  }
  
  /* Hide even more items on extra small screens */
  .ck.ck-toolbar .ck-button[title*="Font Color"],
  .ck.ck-toolbar .ck-button[title*="Strikethrough"],
  .ck.ck-toolbar .ck-button[title*="Underline"] {
    display: none !important;
  }
}

/* Large screens optimization */
@media (min-width: 1200px) {
  .ck.ck-toolbar {
    padding: 12px 20px !important;
    gap: 6px !important;
  }
  
  .ck.ck-button {
    height: 36px !important;
    min-height: 36px !important;
    min-width: 36px !important;
    padding: 8px 10px !important;
    font-size: 14px !important;
  }
  
  .ck.ck-dropdown__button {
    height: 36px !important;
    min-height: 36px !important;
    padding: 8px 10px !important;
  }
}

/* ==========================================
   ACCESSIBILITY & FOCUS STATES
   ========================================== */

.ck.ck-editor__editable:focus {
  outline: 2px solid var(--ck-color-focus-border) !important;
  outline-offset: -2px !important;
}

.ck.ck-button:focus-visible {
  outline: 2px solid var(--ck-color-focus-border) !important;
  outline-offset: 2px !important;
}

/* ==========================================
   LOADING & ERROR STATES
   ========================================== */

.ck-editor-loading {
  background: #f9fafb;
  border: 1px solid var(--ck-color-base-border);
  border-radius: var(--ck-border-radius);
  padding: 40px;
  text-align: center;
  font-family: var(--ck-font-family);
}

.ck-editor-error {
  background: #fef2f2;
  border: 1px solid #fca5a5;
  border-radius: var(--ck-border-radius);
  padding: 20px;
  color: #dc2626;
  font-family: var(--ck-font-family);
}

/* ==========================================
   UTILITY CLASSES
   ========================================== */

.ck-editor-wrapper.ck-rounded {
  border-radius: var(--ck-border-radius);
}

.ck-editor-wrapper.ck-shadow {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.ck-editor-wrapper.ck-bordered {
  border: 1px solid var(--ck-color-base-border);
}
