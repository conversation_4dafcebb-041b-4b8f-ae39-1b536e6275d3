#!/usr/bin/env node

const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🔧 CKEditor CSS Loading Diagnostic');
console.log('=====================================\n');

// Check if files exist
const componentsDir = path.join(__dirname, 'app', 'components');
const stylesDir = path.join(__dirname, 'app', 'styles');
const nodeModulesDir = path.join(__dirname, 'node_modules');

console.log('📁 File Existence Check:');

const files = [
  { path: path.join(componentsDir, 'MyCKEditor.jsx'), name: 'MyCKEditor.jsx' },
  { path: path.join(componentsDir, 'MyCKEditor.tsx'), name: 'MyCKEditor.tsx' },
  { path: path.join(stylesDir, 'ckeditor.css'), name: 'Custom ckeditor.css' },
  { path: path.join(nodeModulesDir, 'ckeditor5', 'ckeditor5.css'), name: 'CKEditor5 default CSS' },
  { path: path.join(nodeModulesDir, '@ckeditor', 'ckeditor5-react'), name: '@ckeditor/ckeditor5-react' }
];

files.forEach(file => {
  const exists = fs.existsSync(file.path);
  console.log(`   ${exists ? '✅' : '❌'} ${file.name}: ${exists ? 'Found' : 'Missing'}`);
});

console.log('\n📋 CSS Import Analysis:');

// Check MyCKEditor.jsx imports
const myCKEditorPath = path.join(componentsDir, 'MyCKEditor.jsx');
if (fs.existsSync(myCKEditorPath)) {
  const content = fs.readFileSync(myCKEditorPath, 'utf8');
  const hasCKEditor5CSS = content.includes("import 'ckeditor5/ckeditor5.css'");
  const hasCustomCSS = content.includes("import '../styles/ckeditor.css'");
  
  console.log(`   ${hasCKEditor5CSS ? '✅' : '❌'} CKEditor5 default CSS imported: ${hasCKEditor5CSS}`);
  console.log(`   ${hasCustomCSS ? '✅' : '✅'} Custom CSS imported: ${hasCustomCSS}`);
} else {
  console.log('   ❌ MyCKEditor.jsx not found');
}

console.log('\n🎯 Key Fixes Applied:');
console.log('   ✅ Added CKEditor5 default CSS import BEFORE custom CSS');
console.log('   ✅ Updated CSS to work WITH ckeditor5.css, not against it');
console.log('   ✅ Added fallback styles for CSS loading issues');
console.log('   ✅ Removed aggressive overrides that could cause conflicts');

console.log('\n🚨 Common Issues & Solutions:');
console.log('   Issue: "CKEditor" text appears instead of editor');
console.log('   ✅ Solution: Import ckeditor5/ckeditor5.css FIRST');
console.log('');
console.log('   Issue: Toolbar appears vertically');
console.log('   ✅ Solution: CSS conflicts resolved with proper specificity');
console.log('');
console.log('   Issue: Editor appears unstyled');
console.log('   ✅ Solution: Ensure ckeditor5 package is installed');

console.log('\n🔍 Debugging Steps:');
console.log('   1. npm run dev');
console.log('   2. Open browser dev tools');
console.log('   3. Check Network tab for ckeditor5.css loading');
console.log('   4. Check Console for import errors');
console.log('   5. Verify .ck-editor class exists on elements');

console.log('\n💡 If issues persist:');
console.log('   • Clear browser cache completely');
console.log('   • Check if ckeditor5 package is installed: npm ls ckeditor5');
console.log('   • Verify import paths are correct');
console.log('   • Check for TypeScript import errors');

console.log('\n📱 Browser Cache Clear Commands:');
console.log('   • Chrome: Ctrl+Shift+R (hard refresh)');
console.log('   • Firefox: Ctrl+F5');
console.log('   • Safari: Cmd+Option+R');

console.log('\n🎉 CSS loading fix verification complete!');

// Check package.json for ckeditor5 dependency
const packageJsonPath = path.join(__dirname, 'package.json');
if (fs.existsSync(packageJsonPath)) {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const hasCKEditor5 = packageJson.dependencies?.ckeditor5 || packageJson.devDependencies?.ckeditor5;
  const hasCKEditorReact = packageJson.dependencies?.['@ckeditor/ckeditor5-react'] || packageJson.devDependencies?.['@ckeditor/ckeditor5-react'];
  
  console.log('\n📦 Package Dependencies:');
  console.log(`   ${hasCKEditor5 ? '✅' : '❌'} ckeditor5: ${hasCKEditor5 || 'Not installed'}`);
  console.log(`   ${hasCKEditorReact ? '✅' : '❌'} @ckeditor/ckeditor5-react: ${hasCKEditorReact || 'Not installed'}`);
}
